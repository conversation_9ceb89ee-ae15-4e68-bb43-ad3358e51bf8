import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    environment: 'jsdom',
    include: ['**/*.test.ts', '**/*.test.tsx'],
    exclude: ['tests/integration/**/*.ts', 'node_modules/**/*.test.ts'],
    globals: true,
    setupFiles: ['./tests/setup.ts'],
    testTimeout: 60000, // Set global test timeout to 60 seconds
  },
})
