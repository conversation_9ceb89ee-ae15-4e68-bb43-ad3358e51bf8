import { NextRequest, NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';
import { TaxInvoiceData } from '@/lib/types/invoice';
import { processTaxInvoiceFile } from '@/lib/api/external-service';
import { validateApiKey } from '@/lib/api/api-key-middleware';
import {
  createTaxInvoiceErrorResponse,
  createProcessingLog,
  updateTaxInvoiceLogWithResults,
  updateLogWithError,
  createApiResponse,
  extractTotalPages,
  getTotalPagesFromBuffer,
  updateLogWithApiKeyId,
  parseFormDataWithFormidable
} from '@/lib/api/shared-invoice-processing';

// Use Node.js runtime for stream and fs support
export const runtime = 'nodejs';

// Disable body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * Process and match a tax invoice file upload
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  return await Sentry.startSpan(
    {
      op: 'http.server',
      name: 'POST /api/process-tax-invoice-file',
      attributes: {
        'http.method': 'POST',
        'http.route': '/api/process-tax-invoice-file',
      },
    },
    async (span) => {
      try {
        // Validate API key
        const validation = await Sentry.startSpan(
          {
            op: 'auth.validate',
            name: 'Validate API Key',
          },
          async () => await validateApiKey(req)
        );

        // Handle API key validation
        if (validation && 'error' in validation && validation.error) {
          span.setStatus({ code: 2, message: 'API key validation failed' });
          span.setAttribute('auth.result', 'failed');
          // Return error response if validation failed
          return validation.error;
        }

        span.setAttribute('auth.result', 'success');
        if (validation && 'apiKeyId' in validation && validation.apiKeyId) {
          span.setAttribute('auth.api_key_id', validation.apiKeyId);
        }

        // Use Formidable to parse multipart form data
        const { fields, files } = await Sentry.startSpan(
          {
            op: 'request.parse',
            name: 'Parse multipart form data',
          },
          async () => await parseFormDataWithFormidable(req)
        );
        const pdfFile = files['file'];

        // Check if file is provided
        if (!pdfFile) {
          span.setStatus({ code: 2, message: 'Missing PDF file' });
          span.setAttribute('validation.result', 'failed');
          span.setAttribute('validation.error', 'missing_file');
          return NextResponse.json(
            {
              error: 'Missing PDF file',
              results: { success: false }
            },
            { status: 400 }
          );
        }

        span.setAttribute('file.name', pdfFile.filename || 'unknown');
        span.setAttribute('file.size', pdfFile.buffer.length);

        // Validate required parameters using shared utility
        const requiredFields: Array<{ key: string; name: string; type: 'string' | 'number' | 'date' }> = [
          { key: 'vendor_name', name: 'vendor_name', type: 'string' },
          { key: 'tax_invoice_number', name: 'tax_invoice_number', type: 'string' },
          { key: 'tax_invoice_date', name: 'tax_invoice_date', type: 'date' },
          { key: 'invoice_amount', name: 'invoice_amount', type: 'number' },
          { key: 'vat_amount', name: 'vat_amount', type: 'number' },
        ];

        const { validateInvoiceFields } = await import('@/lib/api/validate-invoice-fields');
        const formValues: Record<string, unknown> = {};
        for (const field of requiredFields) {
          const value = fields[field.key];
          if (field.type === 'number' && typeof value === 'string') {
            formValues[field.key] = parseFloat(value);
          } else {
            formValues[field.key] = value;
          }
        }

        const validationError = await Sentry.startSpan(
          {
            op: 'validation.fields',
            name: 'Validate tax invoice fields',
          },
          async () => validateInvoiceFields(formValues, requiredFields)
        );

        if (validationError) {
          span.setStatus({ code: 2, message: 'Field validation failed' });
          span.setAttribute('validation.result', 'failed');
          span.setAttribute('validation.error', validationError);
          return NextResponse.json(
            {
              error: validationError,
              results: { success: false }
            },
            { status: 400 }
          );
        }

        span.setAttribute('validation.result', 'success');

        // Extract input data from parsed fields
        const inputData: TaxInvoiceData = {
          vendorName: fields['vendor_name'] || undefined,
          taxInvoiceNo: fields['tax_invoice_number'] || undefined,
          invoiceDate: fields['tax_invoice_date'] || undefined,
          invoiceAmount: fields['invoice_amount'] ?
            parseFloat(fields['invoice_amount']) : undefined,
          vatAmount: parseFloat(fields['vat_amount']), // vat_amount is required for tax invoices
        };

        // Use the buffer from the parsed file
        const buffer = pdfFile.buffer;

        // Create a log entry for this request
        const log = await Sentry.startSpan(
          {
            op: 'db.create',
            name: 'Create processing log',
          },
          async () => await createProcessingLog(
            'taxInvoice',
            pdfFile.filename,
            buffer.length,
            inputData
          )
        );

        if (!log || !log.id) {
          throw new Error('Failed to create processing log');
        }

        span.setAttribute('log.id', log.id);

        // If we have an API key ID, update the log with it
        if (validation && 'apiKeyId' in validation && validation.apiKeyId) {
          await Sentry.startSpan(
            {
              op: 'db.update',
              name: 'Update log with API key ID',
            },
            async () => await updateLogWithApiKeyId(log.id, validation.apiKeyId!)
          );
        }

        try {
          // Start both external service processing and page counting in parallel
          // These operations are independent and can run concurrently for better performance
          const externalResponsePromise = Sentry.startSpan(
            {
              op: 'http.client',
              name: 'Process tax invoice with external service',
              attributes: {
                'external.service': 'DONA',
                'external.operation': 'process_tax_invoice_file',
              },
            },
            async () => processTaxInvoiceFile(buffer, inputData)
          );

          const pageCountPromise = Sentry.startSpan(
            {
              op: 'processing.page_count',
              name: 'Count PDF pages',
              attributes: {
                'pdf.size_bytes': buffer.length,
              },
            },
            async () => getTotalPagesFromBuffer(buffer)
          );

          // Wait for both operations to complete in parallel
          const [externalResponse, pageCount] = await Promise.all([
            externalResponsePromise,
            pageCountPromise
          ]);

          // Use shared matching utility for robust field comparison
          const { matchFields } = await import('@/lib/api/field-matching');
          const fieldTypes: Record<string, "string" | "number" | "date"> = {
            vendor_name: 'string',
            tax_invoice_number: 'string',
            tax_invoice_date: 'date',
            invoice_amount: 'number',
            vat_amount: 'number',
          };

          const { fields: enhancedFields, summary: enhancedSummary } = await Sentry.startSpan(
            {
              op: 'processing.field_matching',
              name: 'Match tax invoice fields',
              attributes: {
                'fields.total': Object.keys(fieldTypes).length,
              },
            },
            async () => matchFields(
              {
                vendor_name: inputData.vendorName,
                tax_invoice_number: inputData.taxInvoiceNo,
                tax_invoice_date: inputData.invoiceDate,
                invoice_amount: inputData.invoiceAmount,
                vat_amount: inputData.vatAmount,
              } as Record<string, string | number | Date>,
              externalResponse.results.fields,
              fieldTypes
            )
          );

          // Use our page count, or fall back to DONA's total_pages if our count fails
          const totalPages = pageCount > 0 ? pageCount : extractTotalPages(externalResponse);

          // Add total_pages to the results
          const enhancedResults = {
            ...externalResponse.results,
            fields: enhancedFields,
            summary: enhancedSummary,
            total_pages: totalPages
          };

          // Add processing metrics to span
          span.setAttribute('processing.external_response_id', externalResponse.processing_id || 'unknown');
          span.setAttribute('processing.total_pages', totalPages || 0);
          span.setAttribute('matching.total_fields', enhancedSummary.total_fields);
          span.setAttribute('matching.matched_fields', enhancedSummary.matched);
          span.setAttribute('matching.mismatched_fields', enhancedSummary.mismatched);
          span.setAttribute('matching.not_found_fields', enhancedSummary.not_found);

          // Update the log with the results
          await Sentry.startSpan(
            {
              op: 'db.update',
              name: 'Update log with results',
            },
            async () => await updateTaxInvoiceLogWithResults(log.id, externalResponse, enhancedResults)
          );

          span.setStatus({ code: 1, message: 'Success' });

          // Return the response in the format expected by the client
          return NextResponse.json(createApiResponse(externalResponse, log.id, enhancedResults));

        } catch (error) {
          // Capture error in Sentry
          Sentry.captureException(error, {
            tags: {
              operation: 'tax_invoice_file_processing',
              log_id: log?.id,
            },
            extra: {
              file_name: pdfFile?.filename,
              file_size: pdfFile?.buffer?.length,
              input_data: inputData,
            },
          });

          span.setStatus({ code: 2, message: error instanceof Error ? error.message : 'Unknown error' });
          span.setAttribute('error.type', error instanceof Error ? error.constructor.name : 'unknown');
          span.setAttribute('error.message', error instanceof Error ? error.message : 'Unknown error');

          // Update the log with the error
          await updateLogWithError(log.id, error);

          // Return error response
          return NextResponse.json(
            createTaxInvoiceErrorResponse(error, log.id),
            { status: 500 }
          );
        }

      } catch (error) {
        // Capture error in Sentry for outer catch
        Sentry.captureException(error, {
          tags: {
            operation: 'tax_invoice_file_processing_outer',
            level: 'critical',
          },
          extra: {
            error_location: 'main_try_block',
          },
        });

        span.setStatus({ code: 2, message: error instanceof Error ? error.message : 'Unknown error' });
        span.setAttribute('error.type', error instanceof Error ? error.constructor.name : 'unknown');
        span.setAttribute('error.message', error instanceof Error ? error.message : 'Unknown error');
        span.setAttribute('error.location', 'main_try_block');

        if (process.env.NODE_ENV !== 'test') {
          console.error('Error processing tax invoice file:', error);
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return NextResponse.json(
          {
            error: errorMessage,
            results: { success: false }
          },
          { status: 500 }
        );
      }
    }
  );
}
