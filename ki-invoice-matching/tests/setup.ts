import '@testing-library/jest-dom'
import { afterAll, afterEach, beforeAll, vi } from 'vitest'
import { setupServer } from 'msw/node'
import { handlers } from './mocks/handlers'

// Mock Sentry globally for all tests
vi.mock('@sentry/nextjs', () => ({
  startSpan: vi.fn(async (config, callback) => {
    // Execute the callback directly, bypassing Sentry span
    if (typeof callback === 'function') {
      const result = await callback({
        setAttribute: vi.fn(),
        setStatus: vi.fn(),
      })
      return result
    }
    return Promise.resolve()
  }),
  captureException: vi.fn(),
  captureRequestError: vi.fn(),
  init: vi.fn(),
}))

// Setup MSW server for API mocking
export const server = setupServer(...handlers)

// Start server before all tests
beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))

// Reset handlers after each test
afterEach(() => server.resetHandlers())

// Close server after all tests
afterAll(() => server.close())
